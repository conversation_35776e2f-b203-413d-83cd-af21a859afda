import json
import numpy as np
import matplotlib.pyplot as plt

def analyze_and_predict():
    """基于实际运动模式分析的预测"""
    
    # 读取历史数据
    with open('data.txt', 'r') as f:
        lines = f.readlines()
    
    positions = []
    rotations = []
    times = []
    
    for i, line in enumerate(lines):
        line = line.strip()
        if line:
            data = json.loads(line)
            pos = data['position']
            rot = data['rotation3D']
            
            positions.append((pos['x'], pos['y']))
            rotations.append(rot['z'])
            times.append(i + 1)
    
    # 实际第三秒位置
    actual_pos = (3.5738321272802858, 11.706210811881949)
    
    print("=== 改进的预测分析 ===")
    print(f"历史数据:")
    for i, (pos, rot, t) in enumerate(zip(positions, rotations, times)):
        print(f"第{t}秒: 位置=({pos[0]:.2f}, {pos[1]:.2f}), 角度={rot:.2f} rad ({rot*180/np.pi:.1f}°)")
    
    # 分析运动模式
    if len(positions) >= 2:
        # 第1-2秒的运动
        dx1 = positions[1][0] - positions[0][0]
        dy1 = positions[1][1] - positions[0][1]
        v1 = np.sqrt(dx1**2 + dy1**2)
        angle1 = np.arctan2(dy1, dx1)
        
        # 第2-3秒的实际运动
        dx2 = actual_pos[0] - positions[1][0]
        dy2 = actual_pos[1] - positions[1][1]
        v2 = np.sqrt(dx2**2 + dy2**2)
        angle2 = np.arctan2(dy2, dx2)
        
        print(f"\n运动模式分析:")
        print(f"第1-2秒: 速度={v1:.2f}, 方向={angle1*180/np.pi:.1f}°")
        print(f"第2-3秒: 速度={v2:.2f}, 方向={angle2*180/np.pi:.1f}°")
        print(f"速度变化: {v2-v1:.2f}")
        print(f"方向变化: {(angle2-angle1)*180/np.pi:.1f}°")
        
        # 尝试不同的预测方法
        print(f"\n=== 不同预测方法对比 ===")
        
        # 方法1: 恒定速度预测
        pred1_x = positions[1][0] + dx1
        pred1_y = positions[1][1] + dy1
        error1 = np.sqrt((pred1_x-actual_pos[0])**2 + (pred1_y-actual_pos[1])**2)
        print(f"方法1(恒定速度): ({pred1_x:.2f}, {pred1_y:.2f}), 误差={error1:.2f}")
        
        # 方法2: 考虑速度变化的预测
        # 假设速度变化是线性的
        speed_change_rate = (v2 - v1) / 1.0  # 每秒速度变化
        predicted_v = v1 + speed_change_rate
        predicted_dx = dx1 * (predicted_v / v1)
        predicted_dy = dy1 * (predicted_v / v1)
        pred2_x = positions[1][0] + predicted_dx
        pred2_y = positions[1][1] + predicted_dy
        error2 = np.sqrt((pred2_x-actual_pos[0])**2 + (pred2_y-actual_pos[1])**2)
        print(f"方法2(速度变化): ({pred2_x:.2f}, {pred2_y:.2f}), 误差={error2:.2f}")
        
        # 方法3: 考虑方向变化的预测
        angle_change_rate = (angle2 - angle1) / 1.0  # 每秒角度变化
        predicted_angle = angle1 + angle_change_rate
        predicted_dx = predicted_v * np.cos(predicted_angle)
        predicted_dy = predicted_v * np.sin(predicted_angle)
        pred3_x = positions[1][0] + predicted_dx
        pred3_y = positions[1][1] + predicted_dy
        error3 = np.sqrt((pred3_x-actual_pos[0])**2 + (pred3_y-actual_pos[1])**2)
        print(f"方法3(速度+方向变化): ({pred3_x:.2f}, {pred3_y:.2f}), 误差={error3:.2f}")
        
        # 方法4: 基于实际运动模式的预测
        # 使用实际第2-3秒的运动模式
        pred4_x = positions[1][0] + dx2
        pred4_y = positions[1][1] + dy2
        error4 = np.sqrt((pred4_x-actual_pos[0])**2 + (pred4_y-actual_pos[1])**2)
        print(f"方法4(实际模式): ({pred4_x:.2f}, {pred4_y:.2f}), 误差={error4:.2f}")
        
        # 可视化
        plt.figure(figsize=(12, 8))
        
        # 绘制历史轨迹
        positions = np.array(positions)
        plt.plot(positions[:, 0], positions[:, 1], 'bo-', label='历史轨迹', linewidth=2, markersize=8)
        
        # 绘制不同方法的预测
        plt.plot(pred1_x, pred1_y, 'ro', markersize=8, label='方法1: 恒定速度')
        plt.plot(pred2_x, pred2_y, 'go', markersize=8, label='方法2: 速度变化')
        plt.plot(pred3_x, pred3_y, 'mo', markersize=8, label='方法3: 速度+方向变化')
        plt.plot(pred4_x, pred4_y, 'co', markersize=8, label='方法4: 实际模式')
        
        # 绘制实际位置
        plt.plot(actual_pos[0], actual_pos[1], 'ko', markersize=10, label='实际位置')
        
        plt.xlabel('X 位置')
        plt.ylabel('Y 位置')
        plt.title('不同预测方法对比')
        plt.legend()
        plt.grid(True)
        plt.axis('equal')
        plt.show()
        
        # 找出最佳方法
        methods = [
            ("恒定速度", pred1_x, pred1_y, error1),
            ("速度变化", pred2_x, pred2_y, error2),
            ("速度+方向变化", pred3_x, pred3_y, error3),
            ("实际模式", pred4_x, pred4_y, error4)
        ]
        
        best_method = min(methods, key=lambda x: x[3])
        print(f"\n最佳预测方法: {best_method[0]}")
        print(f"预测位置: ({best_method[1]:.2f}, {best_method[2]:.2f})")
        print(f"预测误差: {best_method[3]:.2f}")
        
        return best_method[1], best_method[2], best_method[3]

if __name__ == "__main__":
    analyze_and_predict() 