import json
import numpy as np
import matplotlib.pyplot as plt
from filterpy.kalman import ExtendedKalmanFilter
from filterpy.stats import plot_covariance_ellipse
import re

class VehicleEKFWithRotation(ExtendedKalmanFilter):
    def __init__(self, dt):
        # 状态: [x, y, θ, vx, vy, ax, ay] - 位置、角度、速度和加速度
        super().__init__(dim_x=7, dim_z=3)
        self.dt = dt
        
    def predict(self, u=None):
        # 运动模型：包含加速度
        F = np.array([
            [1, 0, 0, self.dt, 0, 0.5*self.dt**2, 0],
            [0, 1, 0, 0, self.dt, 0, 0.5*self.dt**2],
            [0, 0, 1, 0, 0, 0, 0],  # 角度保持不变
            [0, 0, 0, 1, 0, self.dt, 0],  # 速度受加速度影响
            [0, 0, 0, 0, 1, 0, self.dt],
            [0, 0, 0, 0, 0, 1, 0],  # 加速度保持不变
            [0, 0, 0, 0, 0, 0, 1]
        ])
        
        self.x = F @ self.x
        self.P = F @ self.P @ F.T + self.Q
        
    def update(self, z):
        # 测量模型：位置和角度测量
        H = np.array([
            [1, 0, 0, 0, 0, 0, 0],  # x位置
            [0, 1, 0, 0, 0, 0, 0],  # y位置
            [0, 0, 1, 0, 0, 0, 0]   # 角度
        ])
        
        y = z - H @ self.x
        S = H @ self.P @ H.T + self.R
        K = self.P @ H.T @ np.linalg.inv(S)
        
        self.x = self.x + K @ y
        self.P = (np.eye(7) - K @ H) @ self.P

def read_position_rotation_data(filename):
    """读取包含位置和旋转信息的数据"""
    positions = []
    rotations = []
    times = []
    
    with open(filename, 'r') as f:
        lines = f.readlines()
    
    for i, line in enumerate(lines):
        line = line.strip()
        if line:
            try:
                data = json.loads(line)
                
                # 提取位置信息
                pos = data['position']
                x, y = pos['x'], pos['y']
                positions.append((x, y))
                
                # 提取旋转信息 (使用z轴旋转角)
                rot = data['rotation3D']
                angle = rot['z']  # 使用z轴旋转角
                rotations.append(angle)
                
                times.append(i + 1)  # 第一行是1秒，第二行是2秒
                
            except (json.JSONDecodeError, KeyError) as e:
                print(f"警告: 无法解析第{i+1}行: {e}")
                print(f"行内容: {line}")
                continue
    
    return positions, rotations, times

def predict_vehicle_with_rotation(positions, rotations, times, predict_time=3.0):
    """
    基于位置和旋转数据预测第三秒的状态 - 使用方法3（速度+方向变化）
    
    Args:
        positions: 位置数据 [(x1,y1), (x2,y2), ...]
        rotations: 旋转角度数据 [θ1, θ2, ...]
        times: 对应的时间戳 [t1, t2, ...]
        predict_time: 要预测的时间点 (默认3秒)
    
    Returns:
        predicted_position: 预测的位置
        predicted_angle: 预测的角度
        predicted_velocity: 预测的速度
        uncertainty: 位置不确定性
    """
    
    if len(positions) < 2:
        return None, None, None, None, None
    
    # 分析运动模式
    # 第1-2秒的运动
    dx1 = positions[1][0] - positions[0][0]
    dy1 = positions[1][1] - positions[0][1]
    v1 = np.sqrt(dx1**2 + dy1**2)
    angle1 = np.arctan2(dy1, dx1)
    
    # 第2-3秒的实际运动（如果已知）
    # 这里我们使用方法3的逻辑，但基于历史数据推断
    # 假设速度和方向变化是线性的
    
    # 计算速度变化率（基于历史数据推断）
    # 如果没有实际第3秒数据，我们假设速度变化是渐进的
    speed_change_rate = 0.5  # 假设每秒速度增加0.5
    predicted_v = v1 + speed_change_rate
    
    # 计算方向变化率（基于角度变化）
    if len(rotations) >= 2:
        angle_change_rate = (rotations[1] - rotations[0]) / 1.0  # 每秒角度变化
    else:
        angle_change_rate = 0.1  # 假设每秒角度变化0.1弧度
    
    # 预测第3秒的角度
    predicted_angle = angle1 + angle_change_rate
    
    # 预测第3秒的位置
    predicted_dx = predicted_v * np.cos(predicted_angle)
    predicted_dy = predicted_v * np.sin(predicted_angle)
    predicted_x = positions[1][0] + predicted_dx
    predicted_y = positions[1][1] + predicted_dy
    
    # 计算不确定性（基于历史数据的变化）
    # 不确定性随着预测时间增加而增加
    time_factor = predict_time - times[-1]
    base_uncertainty = 0.5
    uncertainty_scale = base_uncertainty * (1 + time_factor * 0.5)
    
    # 创建不确定性矩阵
    uncertainty = np.array([
        [uncertainty_scale**2, 0],
        [0, uncertainty_scale**2]
    ])
    
    predicted_position = (predicted_x, predicted_y)
    predicted_velocity = (predicted_dx, predicted_dy)
    
    return predicted_position, predicted_angle, predicted_velocity, None, uncertainty

def visualize_prediction_with_rotation(positions, rotations, times, predicted_pos, predicted_angle, uncertainty):
    """可视化预测结果，包含置信区间"""
    plt.figure(figsize=(12, 8))
    
    # 绘制历史轨迹
    positions = np.array(positions)
    plt.plot(positions[:, 0], positions[:, 1], 'bo-', label='历史轨迹', linewidth=2, markersize=8)
    
    # 绘制预测位置
    plt.plot(predicted_pos[0], predicted_pos[1], 'ro', markersize=10, label='预测位置')
    
    # 绘制置信区间椭圆
    if uncertainty is not None:
        # 绘制不同置信水平的椭圆
        confidence_levels = [0.5, 1.0, 1.5, 2.0]  # 标准差倍数
        colors = ['lightblue', 'blue', 'darkblue', 'navy']
        alphas = [0.3, 0.2, 0.15, 0.1]
        
        for i, (std, color, alpha) in enumerate(zip(confidence_levels, colors, alphas)):
            plot_covariance_ellipse(
                predicted_pos, uncertainty, 
                std=std, facecolor=color, alpha=alpha
            )
            if i == 0:  # 只在第一个椭圆上添加标签
                plt.text(predicted_pos[0] + std*2, predicted_pos[1], 
                        f'{int(std*100)}% 置信区间', 
                        fontsize=8, ha='center', va='bottom',
                        bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
    
    # 添加角度信息
    plt.text(0.02, 0.98, f'预测角度: {predicted_angle:.2f} rad ({predicted_angle*180/np.pi:.1f}°)', 
             transform=plt.gca().transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    # 添加预测方法说明
    plt.text(0.02, 0.92, '预测方法: 速度+方向变化模型', 
             transform=plt.gca().transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.5))
    
    plt.xlabel('X 位置')
    plt.ylabel('Y 位置')
    plt.title('车辆位置预测 (方法3: 速度+方向变化)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    plt.show()

def main():
    # 读取数据
    positions, rotations, times = read_position_rotation_data('data.txt')
    
    print("读取到的数据:")
    for i, (pos, rot, t) in enumerate(zip(positions, rotations, times)):
        print(f"第{t}秒: 位置=({pos[0]:.2f}, {pos[1]:.2f}), 角度={rot:.2f} rad ({rot*180/np.pi:.1f}°)")
    
    if len(positions) < 2:
        print("错误: 需要至少两个数据点")
        return
    
    # 预测第三秒的状态 - 使用方法3
    predicted_pos, predicted_angle, predicted_vel, predicted_acceleration, uncertainty = predict_vehicle_with_rotation(
        positions, rotations, times, 3.0)
    
    if predicted_pos is None:
        print("预测失败")
        return
    
    print(f"\n预测结果 (方法3: 速度+方向变化):")
    print(f"第三秒位置: x={predicted_pos[0]:.2f}, y={predicted_pos[1]:.2f}")
    print(f"第三秒角度: {predicted_angle:.2f} rad ({predicted_angle*180/np.pi:.1f}°)")
    print(f"第三秒速度: vx={predicted_vel[0]:.2f}, vy={predicted_vel[1]:.2f}")
    print(f"速度大小: {np.sqrt(predicted_vel[0]**2 + predicted_vel[1]**2):.2f}")
    
    if uncertainty is not None:
        print(f"位置不确定性矩阵:")
        print(uncertainty)
        
        # 计算不确定性半径（标准差）
        uncertainty_radius = np.sqrt(uncertainty[0,0] + uncertainty[1,1])
        print(f"位置不确定性半径: {uncertainty_radius:.2f}")
        
        # 输出详细结果
        print(f"\n详细预测信息:")
        print(f"预测坐标: ({predicted_pos[0]:.6f}, {predicted_pos[1]:.6f})")
        print(f"预测角度: {predicted_angle:.6f} rad ({predicted_angle*180/np.pi:.1f}°)")
        print(f"预测速度: ({predicted_vel[0]:.6f}, {predicted_vel[1]:.6f})")
        print(f"速度大小: {np.sqrt(predicted_vel[0]**2 + predicted_vel[1]**2):.6f}")
        print(f"95%置信区间半径: {uncertainty_radius * 2:.2f}")
        print(f"预测时间: 第3秒")
        print(f"预测方法: 速度+方向变化模型")
    
    # 可视化结果
    visualize_prediction_with_rotation(positions, rotations, times, predicted_pos, predicted_angle, uncertainty)

if __name__ == "__main__":
    main() 