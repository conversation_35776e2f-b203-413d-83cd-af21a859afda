import json
import numpy as np
import matplotlib.pyplot as plt

def analyze_prediction_accuracy():
    """分析预测准确性"""
    
    # 读取历史数据
    with open('data.txt', 'r') as f:
        lines = f.readlines()
    
    positions = []
    rotations = []
    times = []
    
    for i, line in enumerate(lines):
        line = line.strip()
        if line:
            data = json.loads(line)
            pos = data['position']
            rot = data['rotation3D']
            
            positions.append((pos['x'], pos['y']))
            rotations.append(rot['z'])
            times.append(i + 1)
    
    # 实际第三秒位置
    actual_pos = (3.5738321272802858, 11.706210811881949)
    
    # 预测结果
    predicted_pos = (6.293968, 12.680470)
    
    print("=== 预测准确性分析 ===")
    print(f"历史数据:")
    for i, (pos, rot, t) in enumerate(zip(positions, rotations, times)):
        print(f"第{t}秒: 位置=({pos[0]:.2f}, {pos[1]:.2f}), 角度={rot:.2f} rad ({rot*180/np.pi:.1f}°)")
    
    print(f"\n预测结果: ({predicted_pos[0]:.2f}, {predicted_pos[1]:.2f})")
    print(f"实际位置: ({actual_pos[0]:.2f}, {actual_pos[1]:.2f})")
    
    # 计算误差
    error_x = predicted_pos[0] - actual_pos[0]
    error_y = predicted_pos[1] - actual_pos[1]
    error_distance = np.sqrt(error_x**2 + error_y**2)
    
    print(f"\n误差分析:")
    print(f"X方向误差: {error_x:.2f}")
    print(f"Y方向误差: {error_y:.2f}")
    print(f"总距离误差: {error_distance:.2f}")
    
    # 分析运动模式
    print(f"\n运动模式分析:")
    if len(positions) >= 2:
        # 第1秒到第2秒的运动
        dx1 = positions[1][0] - positions[0][0]
        dy1 = positions[1][1] - positions[0][1]
        v1 = np.sqrt(dx1**2 + dy1**2)
        
        # 第2秒到第3秒的实际运动
        dx2 = actual_pos[0] - positions[1][0]
        dy2 = actual_pos[1] - positions[1][1]
        v2 = np.sqrt(dx2**2 + dy2**2)
        
        print(f"第1-2秒运动: dx={dx1:.2f}, dy={dy1:.2f}, 速度={v1:.2f}")
        print(f"第2-3秒实际运动: dx={dx2:.2f}, dy={dy2:.2f}, 速度={v2:.2f}")
        
        # 检查速度变化
        speed_change = v2 - v1
        print(f"速度变化: {speed_change:.2f}")
        
        # 检查方向变化
        angle1 = np.arctan2(dy1, dx1)
        angle2 = np.arctan2(dy2, dx2)
        angle_change = (angle2 - angle1) * 180 / np.pi
        print(f"方向变化: {angle_change:.1f}°")
    
    # 可视化
    plt.figure(figsize=(12, 8))
    
    # 绘制历史轨迹
    positions = np.array(positions)
    plt.plot(positions[:, 0], positions[:, 1], 'bo-', label='历史轨迹', linewidth=2, markersize=8)
    
    # 绘制预测位置
    plt.plot(predicted_pos[0], predicted_pos[1], 'ro', markersize=10, label='预测位置')
    
    # 绘制实际位置
    plt.plot(actual_pos[0], actual_pos[1], 'go', markersize=10, label='实际位置')
    
    # 绘制误差线
    plt.plot([predicted_pos[0], actual_pos[0]], [predicted_pos[1], actual_pos[1]], 
             'r--', alpha=0.7, label='预测误差')
    
    plt.xlabel('X 位置')
    plt.ylabel('Y 位置')
    plt.title('预测准确性分析')
    plt.legend()
    plt.grid(True)
    plt.axis('equal')
    
    # 添加误差标注
    plt.annotate(f'误差: {error_distance:.2f}', 
                xy=((predicted_pos[0] + actual_pos[0])/2, (predicted_pos[1] + actual_pos[1])/2),
                xytext=(10, 10), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
    
    plt.show()
    
    return error_distance, error_x, error_y

if __name__ == "__main__":
    analyze_prediction_accuracy() 