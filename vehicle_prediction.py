import numpy as np
import matplotlib.pyplot as plt
from filterpy.kalman import ExtendedKalmanFilter
from filterpy.stats import plot_covariance_ellipse

class VehicleEKF(ExtendedKalmanFilter):
    def __init__(self, dt):
        # 状态: [x, y, vx, vy] - 位置和速度
        super().__init__(dim_x=4, dim_z=2)
        self.dt = dt
        
    def predict(self, u=None):
        # 恒定速度模型
        F = np.array([
            [1, 0, self.dt, 0],
            [0, 1, 0, self.dt],
            [0, 0, 1, 0],
            [0, 0, 0, 1]
        ])
        
        self.x = F @ self.x
        self.P = F @ self.P @ F.T + self.Q
        
    def update(self, z):
        # 直接位置测量
        H = np.array([
            [1, 0, 0, 0],
            [0, 1, 0, 0]
        ])
        
        y = z - H @ self.x
        S = H @ self.P @ H.T + self.R
        K = self.P @ H.T @ np.linalg.inv(S)
        
        self.x = self.x + K @ y
        self.P = (np.eye(4) - K @ H) @ self.P

def predict_vehicle_position(positions, times, predict_time=3.0):
    """
    基于前两秒的位置数据预测第三秒的位置
    
    Args:
        positions: 前两秒的位置数据 [(x1,y1), (x2,y2), ...]
        times: 对应的时间戳 [t1, t2, ...]
        predict_time: 要预测的时间点 (默认3秒)
    
    Returns:
        predicted_position: 预测的位置
        uncertainty: 位置不确定性
    """
    
    # 初始化EKF
    dt = 0.1  # 时间步长
    ekf = VehicleEKF(dt)
    
    # 初始状态估计
    if len(positions) >= 2:
        # 用前两个位置估计初始速度和位置
        x1, y1 = positions[0]
        x2, y2 = positions[1]
        t1, t2 = times[0], times[1]
        
        dt_obs = t2 - t1
        vx = (x2 - x1) / dt_obs
        vy = (y2 - y1) / dt_obs
        
        ekf.x = np.array([[x2, y2, vx, vy]]).T
    else:
        ekf.x = np.array([[positions[0][0], positions[0][1], 0, 0]]).T
    
    # 设置噪声参数
    ekf.P = np.diag([1, 1, 2, 2])  # 位置和速度的不确定性
    ekf.Q = np.diag([0.1, 0.1, 0.5, 0.5])  # 过程噪声
    ekf.R = np.diag([0.5, 0.5])  # 测量噪声
    
    # 运行EKF
    current_time = times[-1]
    while current_time < predict_time:
        ekf.predict()
        current_time += dt
    
    # 返回预测结果
    predicted_position = (ekf.x[0, 0], ekf.x[1, 0])
    uncertainty = ekf.P[0:2, 0:2]
    
    return predicted_position, uncertainty

def visualize_prediction(positions, times, predicted_pos, uncertainty):
    """可视化预测结果"""
    plt.figure(figsize=(10, 8))
    
    # 绘制历史轨迹
    positions = np.array(positions)
    plt.plot(positions[:, 0], positions[:, 1], 'bo-', label='历史轨迹', linewidth=2)
    
    # 绘制预测位置
    plt.plot(predicted_pos[0], predicted_pos[1], 'ro', markersize=10, label='预测位置')
    
    # 绘制不确定性椭圆
    plot_covariance_ellipse(
        predicted_pos, uncertainty, 
        std=2, facecolor='red', alpha=0.3
    )
    
    plt.xlabel('X 位置')
    plt.ylabel('Y 位置')
    plt.title('车辆位置预测')
    plt.legend()
    plt.grid(True)
    plt.axis('equal')
    plt.show()

# 示例使用
if __name__ == "__main__":
    # 模拟前两秒的位置数据
    positions = [
        (0, 0),      # 0秒
        (5, 3),      # 1秒  
        (10, 6),     # 2秒
    ]
    times = [0, 1, 2]
    
    # 预测第三秒的位置
    predicted_pos, uncertainty = predict_vehicle_position(positions, times, 3.0)
    
    print(f"预测位置: {predicted_pos}")
    print(f"位置不确定性: \n{uncertainty}")
    
    # 可视化
    visualize_prediction(positions, times, predicted_pos, uncertainty) 